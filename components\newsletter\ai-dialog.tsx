"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Bot, Sparkles, Globe } from "lucide-react"
import { NewsletterBlock, NewsletterHeaderFooter } from "@/types/newsletter"
import { Spinner } from "../ui/shadcn-io/spinner"
import { useLanguageContext } from "@/contexts/language-context"
import { useAIApi } from "@/hooks/use-ai-api"

interface AIDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  block?: NewsletterBlock | null
  headerFooter?: NewsletterHeaderFooter | null
  newsletterId?: string
  onAIResponse: (response: any, language: string) => void
  debugInfo?: Record<string, any>
}

export function AIDialog({ open, onOpenChange, block, headerFooter, newsletterId, onAIResponse, debugInfo }: AIDialogProps) {
  const { selectedLanguage, languages } = useLanguageContext()
  const { generateContent, loading: apiLoading, error: apiError } = useAIApi()
  const [prompt, setPrompt] = useState("")
  const [selectedAILanguage, setSelectedAILanguage] = useState(selectedLanguage)

  // Update AI language when context language changes
  React.useEffect(() => {
    setSelectedAILanguage(selectedLanguage)
  }, [selectedLanguage])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (apiLoading) return

    try {
      const contextType = block ? 'block' : headerFooter ? 'header_footer' : 'general'

      const response = await generateContent({
        prompt: prompt.trim() || undefined,
        language: selectedAILanguage,
        context: {
          type: contextType,
          blockId: block?.id,
          newsletterId,
          block,
          headerFooter,
          debugInfo
        }
      })

      onAIResponse(response, selectedAILanguage)
      onOpenChange(false)
      setPrompt("")
    } catch (error) {
      console.error('Error calling AI API:', error)
      // Error is already handled by the useAIApi hook
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    setPrompt("")
  }

  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; flag: string }> = {
      'es': { display: 'Espanyol', flag: '🇪🇸' },
      'ca': { display: 'Català', flag: '🏴󠁥󠁳󠁣󠁴󠁿' },
      'fr': { display: 'Francès', flag: '🇫🇷' },
      'en': { display: 'Anglès', flag: '🇬🇧' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), flag: '🌐' }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary" />
            Assistent d'IA
            {block && (
              <span className="text-sm font-normal text-muted-foreground">
                - {block.name}
              </span>
            )}
          </DialogTitle>
          <DialogDescription>
            {block
              ? `Genera contingut per al bloc "${block.name}" utilitzant intel·ligència artificial.`
              : headerFooter
              ? `Genera contingut per a "${headerFooter.name}" utilitzant intel·ligència artificial.`
              : "Genera contingut per al butlletí utilitzant intel·ligència artificial."
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Language Selector */}
          <div className="space-y-2">
            <Label htmlFor="ai-language">Idioma de generació</Label>
            <Select value={selectedAILanguage} onValueChange={setSelectedAILanguage}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {['ca', 'es', 'fr', 'en'].map((langCode) => {
                  const { display, flag } = getLanguageDisplay(langCode)
                  return (
                    <SelectItem key={langCode} value={langCode}>
                      <div className="flex items-center gap-2">
                        <span>{flag}</span>
                        <span>{display}</span>
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Prompt Input */}
          <div className="space-y-2">
            <Label htmlFor="ai-prompt">
              Instruccions per a l'IA <span className="text-muted-foreground">(opcional)</span>
            </Label>
            <Textarea
              id="ai-prompt"
              placeholder={block
                ? `Descriu com vols que l'IA modifiqui el bloc "${block.name}"...`
                : headerFooter
                ? `Descriu com vols que l'IA modifiqui "${headerFooter.name}"...`
                : "Descriu com vols que l'IA generi o modifiqui el contingut del butlletí..."
              }
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              rows={4}
              disabled={apiLoading}
            />
            <p className="text-xs text-muted-foreground">
              Si no especifiques instruccions, l'IA generarà contingut basat en el context actual.
            </p>
          </div>

          {/* Debug Info (if provided) */}
          {debugInfo && (
            <div className="p-3 bg-muted/50 rounded-md">
              <h4 className="text-sm font-medium mb-2">Context</h4>
              <div className="text-xs text-muted-foreground space-y-1">
                {Object.entries(debugInfo).map(([key, value]) => (
                  <div key={key}>
                    <span className="font-medium">{key}:</span> {JSON.stringify(value)}
                  </div>
                ))}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={apiLoading}
            >
              Cancel·lar
            </Button>
            <Button
              type="submit"
              disabled={apiLoading}
              className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
            >
              {apiLoading ? (
                <>
                  <Spinner className="mr-2 h-4 w-4" variant="default" />
                  Generant...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generar amb IA
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
